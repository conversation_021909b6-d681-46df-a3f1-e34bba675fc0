"use client"

import { use<PERSON><PERSON>, use<PERSON>ffe<PERSON>, use<PERSON><PERSON>back } from "react"
import {
  <PERSON>,
  Eye,
  Ear,
  Hand,
  Target,
  Zap,
  Settings,
  RotateCcw,
  Layers,
  Activity,
  Info,
  AlertTriangle,
  Play,
  Pause
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>lider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"

interface Node {
  id: string
  x: number
  y: number
  value: number
  bias: number
  layer: number
  active: boolean
  type: "input" | "hidden" | "output"
}

interface Connection {
  from: string
  to: string
  weight: number
  active: boolean
}

interface BrainRegion {
  id: string
  name: string
  x: number
  y: number
  width: number
  height: number
  active: boolean
  description: string
  color: string
}

interface BrainSignal {
  id: string
  x: number
  y: number
  targetX: number
  targetY: number
  active: boolean
  region: string
}

interface BiologicalNeuron {
  id: string
  x: number
  y: number
  region: string
  active: boolean
  firing: boolean
  dendrites: { x: number; y: number; angle: number }[]
  axon: { x: number; y: number; length: number; angle: number }
  connections: string[] // IDs of connected neurons
}

interface BNNNode {
  id: string
  x: number
  y: number
  layer: number
  type: "input" | "hidden" | "output"
  membrane_potential: number
  threshold: number
  refractory_period: number
  last_spike_time: number
  active: boolean
  firing: boolean
  dendrites: { x: number; y: number; angle: number }[]
  axon: { x: number; y: number; length: number; angle: number }
}

interface Spike {
  id: string
  from: string
  to: string
  x: number
  y: number
  targetX: number
  targetY: number
  active: boolean
  timestamp: number
}

type ActivationFunction = "sigmoid" | "relu" | "tanh" | "leaky_relu"
type StimulusType = "visual" | "auditory" | "motor" | "cognitive"

const activationFunctions = {
  sigmoid: (x: number) => 1 / (1 + Math.exp(-x)),
  relu: (x: number) => Math.max(0, x),
  tanh: (x: number) => Math.tanh(x),
  leaky_relu: (x: number) => x > 0 ? x : 0.01 * x
}

const NeuralNetwork = () => {
  const [mounted, setMounted] = useState(false)
  const [nodes, setNodes] = useState<Node[]>([])
  const [connections, setConnections] = useState<Connection[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [learningRate, setLearningRate] = useState([0.1])
  const [epochs, setEpochs] = useState([100])
  const [currentEpoch, setCurrentEpoch] = useState(0)
  const [loss, setLoss] = useState(0.5)
  const [accuracy, setAccuracy] = useState(0.75)
  const [selectedNode, setSelectedNode] = useState<Node | null>(null)
  const [numHiddenLayers, setNumHiddenLayers] = useState([2])
  const [nodesPerLayer, setNodesPerLayer] = useState([4])
  const [activationFunction, setActivationFunction] = useState<ActivationFunction>("sigmoid")
  const [showNodeDetails, setShowNodeDetails] = useState(true)
  const [brainRegions, setBrainRegions] = useState<BrainRegion[]>([])
  const [brainSignals, setBrainSignals] = useState<BrainSignal[]>([])
  const [selectedStimulus, setSelectedStimulus] = useState<StimulusType>("visual")
  const [showComparison, setShowComparison] = useState(false)


  const [bnnNodes, setBnnNodes] = useState<BNNNode[]>([])
  const [spikes, setSpikes] = useState<Spike[]>([])
  const [bnnRunning, setBnnRunning] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [selectedBNNNode, setSelectedBNNNode] = useState<BNNNode | null>(null)
  const [membranePotentialHistory, setMembranePotentialHistory] = useState<{[key: string]: {time: number, potential: number}[]}>({})
  const [simulationMode, setSimulationMode] = useState<"ANN" | "SNN">("ANN")
  const [showHybridMode, setShowHybridMode] = useState<boolean>(false)
  const [showAnatomicalLabels, setShowAnatomicalLabels] = useState(true)
  const [signalStrength, setSignalStrength] = useState([20]) // mV for EPSP/IPSP
  const [spikeThreshold, setSpikeThreshold] = useState([-55]) // mV threshold

  // Handle hydration
  useEffect(() => {
    setMounted(true)
  }, [])

  // Reset hybrid mode when switching to SNN
  useEffect(() => {
    if (simulationMode === "SNN") {
      setShowHybridMode(false)
    }
  }, [simulationMode])

  // Initialize network structure
  const initializeNetwork = useCallback(() => {
    const networkNodes: Node[] = []
    const networkConnections: Connection[] = []

    const totalLayers = numHiddenLayers[0] + 2 // +2 for input and output
    const layerSpacing = 800 / (totalLayers + 1)

    // Input layer (3 nodes)
    const inputNodes = 3
    for (let i = 0; i < inputNodes; i++) {
      networkNodes.push({
        id: `input-${i}`,
        x: layerSpacing,
        y: 150 + i * 100,
        value: Math.random(),
        bias: 0,
        layer: 0,
        active: false,
        type: "input",
      })
    }

    // Hidden layers
    for (let layer = 1; layer <= numHiddenLayers[0]; layer++) {
      const nodesInLayer = nodesPerLayer[0]
      const startY = 200 - (nodesInLayer * 30) / 2

      for (let i = 0; i < nodesInLayer; i++) {
        networkNodes.push({
          id: `hidden${layer}-${i}`,
          x: layerSpacing * (layer + 1),
          y: startY + i * 60,
          value: 0,
          bias: Math.random() * 0.2 - 0.1,
          layer: layer,
          active: false,
          type: "hidden",
        })
      }
    }

    // Output layer (2 nodes)
    const outputNodes = 2
    for (let i = 0; i < outputNodes; i++) {
      networkNodes.push({
        id: `output-${i}`,
        x: layerSpacing * (totalLayers),
        y: 175 + i * 50,
        value: 0,
        bias: Math.random() * 0.2 - 0.1,
        layer: totalLayers - 1,
        active: false,
        type: "output",
      })
    }

    // Create connections
    networkNodes.forEach((fromNode) => {
      networkNodes.forEach((toNode) => {
        if (toNode.layer === fromNode.layer + 1) {
          networkConnections.push({
            from: fromNode.id,
            to: toNode.id,
            weight: Math.random() * 2 - 1,
            active: false,
          })
        }
      })
    })

    setNodes(networkNodes)
    setConnections(networkConnections)
  }, [numHiddenLayers, nodesPerLayer])

  useEffect(() => {
    initializeNetwork()
  }, [initializeNetwork])

  // Initialize brain regions
  useEffect(() => {
    const regions: BrainRegion[] = [
      {
        id: "frontal-cortex",
        name: "Frontal Cortex",
        x: 200,
        y: 80,
        width: 120,
        height: 60,
        active: false,
        description: "Executive functions, decision making, planning",
        color: "#3b82f6"
      },
      {
        id: "parietal-cortex",
        name: "Parietal Cortex",
        x: 320,
        y: 80,
        width: 100,
        height: 60,
        active: false,
        description: "Sensory integration, spatial processing",
        color: "#8b5cf6"
      },
      {
        id: "occipital-cortex",
        name: "Occipital Cortex",
        x: 420,
        y: 80,
        width: 80,
        height: 60,
        active: false,
        description: "Visual processing",
        color: "#10b981"
      },
      {
        id: "temporal-cortex",
        name: "Temporal Cortex",
        x: 150,
        y: 140,
        width: 100,
        height: 50,
        active: false,
        description: "Auditory processing, memory",
        color: "#f59e0b"
      },
      {
        id: "motor-cortex",
        name: "Motor Cortex",
        x: 250,
        y: 50,
        width: 80,
        height: 30,
        active: false,
        description: "Motor control and movement",
        color: "#ef4444"
      },
      {
        id: "thalamus",
        name: "Thalamus",
        x: 280,
        y: 160,
        width: 60,
        height: 40,
        active: false,
        description: "Relay station for sensory information",
        color: "#06b6d4"
      },
      {
        id: "hippocampus",
        name: "Hippocampus",
        x: 200,
        y: 190,
        width: 70,
        height: 30,
        active: false,
        description: "Memory formation and learning",
        color: "#84cc16"
      },
      {
        id: "brainstem",
        name: "Brainstem",
        x: 300,
        y: 220,
        width: 40,
        height: 60,
        active: false,
        description: "Basic life functions, reflexes",
        color: "#6b7280"
      }
    ]
    setBrainRegions(regions)
  }, [])



  // Initialize BNN (Biological-style Neural Network)
  const initializeBNN = useCallback(() => {
    const bnnNetworkNodes: BNNNode[] = []
    const totalLayers = numHiddenLayers[0] + 2
    const layerSpacing = 800 / (totalLayers + 1)

    // Input layer (3 nodes)
    const inputNodes = 3
    for (let i = 0; i < inputNodes; i++) {
      const nodeX = layerSpacing
      const nodeY = 150 + i * 100

      // Generate dendrites - cleaner pattern like the image
      const numDendrites = 4 // Fixed number for cleaner look
      const dendrites = []
      for (let j = 0; j < numDendrites; j++) {
        const angle = (Math.PI * 2 * j) / numDendrites + Math.PI // Start from left side
        dendrites.push({
          x: nodeX + Math.cos(angle) * 25,
          y: nodeY + Math.sin(angle) * 25,
          angle: angle
        })
      }

      bnnNetworkNodes.push({
        id: `bnn-input-${i}`,
        x: nodeX,
        y: nodeY,
        layer: 0,
        type: "input",
        membrane_potential: -70, // Resting potential in mV
        threshold: spikeThreshold[0], // Spike threshold in mV
        refractory_period: 2, // ms
        last_spike_time: -100,
        active: false,
        firing: false,
        dendrites,
        axon: {
          x: nodeX + 40,
          y: nodeY,
          length: 40,
          angle: 0
        }
      })
    }

    // Hidden layers
    for (let layer = 1; layer <= numHiddenLayers[0]; layer++) {
      const nodesInLayer = nodesPerLayer[0]
      const startY = 200 - (nodesInLayer * 30) / 2

      for (let i = 0; i < nodesInLayer; i++) {
        const nodeX = layerSpacing * (layer + 1)
        const nodeY = startY + i * 60

        const numDendrites = 4
        const dendrites = []
        for (let j = 0; j < numDendrites; j++) {
          const angle = (Math.PI * 2 * j) / numDendrites + Math.PI
          dendrites.push({
            x: nodeX + Math.cos(angle) * 25,
            y: nodeY + Math.sin(angle) * 25,
            angle: angle
          })
        }

        bnnNetworkNodes.push({
          id: `bnn-hidden${layer}-${i}`,
          x: nodeX,
          y: nodeY,
          layer: layer,
          type: "hidden",
          membrane_potential: -70,
          threshold: spikeThreshold[0],
          refractory_period: 2,
          last_spike_time: -100,
          active: false,
          firing: false,
          dendrites,
          axon: {
            x: nodeX + 40,
            y: nodeY,
            length: 40,
            angle: 0
          }
        })
      }
    }

    // Output layer (2 nodes)
    const outputNodes = 2
    for (let i = 0; i < outputNodes; i++) {
      const nodeX = layerSpacing * totalLayers
      const nodeY = 175 + i * 50

      const numDendrites = 4
      const dendrites = []
      for (let j = 0; j < numDendrites; j++) {
        const angle = (Math.PI * 2 * j) / numDendrites + Math.PI
        dendrites.push({
          x: nodeX + Math.cos(angle) * 25,
          y: nodeY + Math.sin(angle) * 25,
          angle: angle
        })
      }

      bnnNetworkNodes.push({
        id: `bnn-output-${i}`,
        x: nodeX,
        y: nodeY,
        layer: totalLayers - 1,
        type: "output",
        membrane_potential: -70,
        threshold: spikeThreshold[0],
        refractory_period: 2,
        last_spike_time: -100,
        active: false,
        firing: false,
        dendrites,
        axon: {
          x: nodeX + 40,
          y: nodeY,
          length: 40,
          angle: 0
        }
      })
    }

    setBnnNodes(bnnNetworkNodes)
  }, [numHiddenLayers, nodesPerLayer, spikeThreshold])

  useEffect(() => {
    if (showComparison || simulationMode === "SNN") {
      initializeBNN()
    } else if (simulationMode === "ANN" && !showComparison) {
      // Clear BNN nodes when not needed
      setBnnNodes([])
      setSpikes([])
      setSelectedBNNNode(null)
      setMembranePotentialHistory({})
    }
  }, [initializeBNN, showComparison, simulationMode])

  // Forward propagation animation
  const runForwardPropagation = useCallback(async () => {
    if (!nodes.length) return

    // Reset all activations
    setNodes((prev) => prev.map((node) => ({ ...node, active: false })))
    setConnections((prev) => prev.map((conn) => ({ ...conn, active: false })))

    // Activate input layer
    await new Promise((resolve) => setTimeout(resolve, 500))
    setNodes((prev) =>
      prev.map((node) => (node.type === "input" ? { ...node, active: true, value: Math.random() } : node)),
    )

    // Process each layer
    const maxLayer = Math.max(...nodes.map(n => n.layer))
    for (let layer = 1; layer <= maxLayer; layer++) {
      await new Promise((resolve) => setTimeout(resolve, 800))

      // Activate connections to this layer
      setConnections((prev) =>
        prev.map((conn) => {
          const toNode = nodes.find((n) => n.id === conn.to)
          return toNode?.layer === layer ? { ...conn, active: true } : conn
        }),
      )

      await new Promise((resolve) => setTimeout(resolve, 400))

      // Activate nodes in this layer
      setNodes((prev) =>
        prev.map((node) => {
          if (node.layer === layer) {
            // Calculate weighted sum (simplified)
            const inputSum = connections
              .filter((conn) => conn.to === node.id)
              .reduce((sum, conn) => {
                const fromNode = prev.find((n) => n.id === conn.from)
                return sum + (fromNode?.value || 0) * conn.weight
              }, 0)

            // Apply selected activation function
            const activatedValue = activationFunctions[activationFunction](inputSum + node.bias)

            return { ...node, active: true, value: activatedValue }
          }
          return node
        }),
      )

      // Deactivate connections after processing
      await new Promise((resolve) => setTimeout(resolve, 600))
      setConnections((prev) => prev.map((conn) => ({ ...conn, active: false })))
    }

    // Update metrics
    setLoss((prev) => Math.max(0.01, prev - 0.02 + Math.random() * 0.01))
    setAccuracy((prev) => Math.min(0.99, prev + 0.01 + Math.random() * 0.005))
    setCurrentEpoch((prev) => prev + 1)
  }, [nodes, connections, activationFunction])

  // Auto-run training
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isRunning && currentEpoch < epochs[0]) {
      interval = setInterval(() => {
        runForwardPropagation()
      }, 3000)
    } else if (currentEpoch >= epochs[0]) {
      setIsRunning(false)
    }
    return () => clearInterval(interval)
  }, [isRunning, currentEpoch, epochs, runForwardPropagation])

  // Reset current epoch if it exceeds max epochs
  useEffect(() => {
    if (currentEpoch >= epochs[0]) {
      setCurrentEpoch(0)
      setIsRunning(false)
    }
  }, [epochs, currentEpoch])

  const resetNetwork = () => {
    setIsRunning(false)
    setBnnRunning(false)
    setCurrentEpoch(0)
    setLoss(0.5)
    setAccuracy(0.75)
    setSelectedNode(null)
    setSelectedBNNNode(null)
    setSpikes([])
    setMembranePotentialHistory({})
    setCurrentTime(0)

    if (simulationMode === "ANN") {
      initializeNetwork()
    } else {
      initializeBNN()
    }
  }

  // Brain signal simulation
  const simulateBrainSignal = useCallback(async (stimulus: StimulusType) => {
    setBrainSignals([])
    setBrainRegions(prev => prev.map(region => ({ ...region, active: false })))

    const pathways = {
      visual: ["occipital-cortex", "parietal-cortex", "frontal-cortex"],
      auditory: ["temporal-cortex", "thalamus", "frontal-cortex"],
      motor: ["frontal-cortex", "motor-cortex", "brainstem"],
      cognitive: ["frontal-cortex", "hippocampus", "thalamus", "parietal-cortex"]
    }

    const pathway = pathways[stimulus]

    for (let i = 0; i < pathway.length; i++) {
      const regionId = pathway[i]
      const region = brainRegions.find(r => r.id === regionId)

      if (region) {
        // Activate region
        setBrainRegions(prev =>
          prev.map(r =>
            r.id === regionId ? { ...r, active: true } : r
          )
        )



        // Create signal if not the last region
        if (i < pathway.length - 1) {
          const nextRegionId = pathway[i + 1]
          const nextRegion = brainRegions.find(r => r.id === nextRegionId)

          if (nextRegion) {
            const signalId = `signal-${i}`
            setBrainSignals(prev => [...prev, {
              id: signalId,
              x: region.x + region.width / 2,
              y: region.y + region.height / 2,
              targetX: nextRegion.x + nextRegion.width / 2,
              targetY: nextRegion.y + nextRegion.height / 2,
              active: true,
              region: regionId
            }])

            // Animate signal movement
            await new Promise(resolve => setTimeout(resolve, 800))

            setBrainSignals(prev => prev.filter(s => s.id !== signalId))
          }
        }

        await new Promise(resolve => setTimeout(resolve, 600))
      }
    }

    // Deactivate all regions after completion
    setTimeout(() => {
      setBrainRegions(prev => prev.map(region => ({ ...region, active: false })))
    }, 1000)
  }, [brainRegions])

  // Update membrane potential history
  const updateMembranePotentialHistory = useCallback((nodeId: string, potential: number, time: number) => {
    setMembranePotentialHistory(prev => {
      const history = prev[nodeId] || []
      const newHistory = [...history, { time, potential }]
      // Keep only last 100 data points
      if (newHistory.length > 100) {
        newHistory.shift()
      }
      return {
        ...prev,
        [nodeId]: newHistory
      }
    })
  }, [])

  // BNN spike propagation simulation
  const runBNNPropagation = useCallback(async () => {
    if (!bnnNodes.length) return

    setBnnRunning(true)
    setSpikes([])
    setCurrentTime(0)

    // Reset all neurons
    setBnnNodes(prev => prev.map(node => ({
      ...node,
      membrane_potential: -70,
      active: false,
      firing: false,
      last_spike_time: -100
    })))

    // Stimulate input neurons with random spikes
    const inputNeurons = bnnNodes.filter(n => n.type === "input")
    inputNeurons.forEach((neuron, index) => {
      setTimeout(() => {
        setBnnNodes(prev => prev.map(n =>
          n.id === neuron.id
            ? { ...n, firing: true, membrane_potential: 30, last_spike_time: currentTime, active: true }
            : n
        ))

        // Generate spike to next layer
        const targetNeurons = bnnNodes.filter(n => n.layer === neuron.layer + 1)
        targetNeurons.forEach(target => {
          const spikeId = `spike-${neuron.id}-${target.id}-${Date.now()}`
          setSpikes(prev => [...prev, {
            id: spikeId,
            from: neuron.id,
            to: target.id,
            x: neuron.axon.x,
            y: neuron.axon.y,
            targetX: target.x,
            targetY: target.y,
            active: true,
            timestamp: currentTime
          }])

          // Remove spike after animation and update target neuron
          setTimeout(() => {
            setSpikes(prev => prev.filter(s => s.id !== spikeId))

            setBnnNodes(prev => prev.map(n => {
              if (n.id === target.id) {
                const newPotential = n.membrane_potential + (Math.random() * signalStrength[0] - signalStrength[0]/3) // EPSP/IPSP
                const shouldFire = newPotential > n.threshold &&
                                 (currentTime - n.last_spike_time) > n.refractory_period

                const finalPotential = shouldFire ? 30 : Math.max(-80, newPotential)

                // Update membrane potential history
                updateMembranePotentialHistory(n.id, finalPotential, currentTime + 800)

                return {
                  ...n,
                  membrane_potential: finalPotential,
                  firing: shouldFire,
                  active: true,
                  last_spike_time: shouldFire ? currentTime : n.last_spike_time
                }
              }
              return n
            }))
          }, 800)
        })

        // Reset firing state
        setTimeout(() => {
          setBnnNodes(prev => prev.map(n =>
            n.id === neuron.id
              ? { ...n, firing: false, membrane_potential: -70 }
              : n
          ))
        }, 300)
      }, index * 200)
    })

    // Propagate through layers with proper timing
    const maxLayer = Math.max(...bnnNodes.map(n => n.layer))
    let currentPropagationLayer = 1

    const processLayerPropagation = () => {
      if (currentPropagationLayer > maxLayer) {
        // Reset all neurons after propagation is complete
        setTimeout(() => {
          setBnnNodes(prev => prev.map(n => ({
            ...n,
            firing: false,
            active: false,
            membrane_potential: -70
          })))
          setSpikes([])
        }, 2000)
        return
      }

      // Get current state of BNN nodes
      setBnnNodes(currentNodes => {
        const firingNeurons = currentNodes.filter(n => n.layer === currentPropagationLayer && n.firing)

        if (firingNeurons.length === 0 && currentPropagationLayer > 1) {
          // No firing neurons in this layer, stop propagation
          return currentNodes
        }

        // Process each firing neuron
        firingNeurons.forEach(neuron => {
          const nextLayerNeurons = currentNodes.filter(n => n.layer === neuron.layer + 1)

          // Send spikes to next layer
          nextLayerNeurons.forEach(target => {
            const spikeId = `spike-${neuron.id}-${target.id}-${Date.now()}-${Math.random()}`

            // Add spike visual
            setTimeout(() => {
              setSpikes(prev => [...prev, {
                id: spikeId,
                from: neuron.id,
                to: target.id,
                x: neuron.axon.x,
                y: neuron.axon.y,
                targetX: target.x,
                targetY: target.y,
                active: true,
                timestamp: Date.now()
              }])
            }, 100)

            // Process spike arrival and update target neuron
            setTimeout(() => {
              setSpikes(prev => prev.filter(s => s.id !== spikeId))

              setBnnNodes(prev => prev.map(n => {
                if (n.id === target.id) {
                  // Calculate new membrane potential with reliable signal
                  // Use full signal strength with small random variation (90-110%)
                  const signalInput = signalStrength[0] * (0.9 + Math.random() * 0.2)
                  const newPotential = n.membrane_potential + signalInput
                  const shouldFire = newPotential > n.threshold &&
                                   (Date.now() - n.last_spike_time) > n.refractory_period

                  // Update membrane potential history
                  updateMembranePotentialHistory(n.id, shouldFire ? 30 : newPotential, Date.now())

                  return {
                    ...n,
                    membrane_potential: shouldFire ? 30 : Math.max(-80, newPotential),
                    firing: shouldFire,
                    active: true,
                    last_spike_time: shouldFire ? Date.now() : n.last_spike_time
                  }
                }
                return n
              }))
            }, 800)
          })
        })

        return currentNodes
      })

      // Move to next layer after processing current layer
      setTimeout(() => {
        currentPropagationLayer++
        processLayerPropagation()
      }, 1500)
    }

    // Start propagation
    processLayerPropagation()

    // Reset after completion
    setTimeout(() => {
      setBnnRunning(false)
      setBnnNodes(prev => prev.map(node => ({
        ...node,
        active: false,
        firing: false,
        membrane_potential: -70
      })))
      setSpikes([])
    }, (maxLayer + 2) * 1000)
  }, [bnnNodes, currentTime, signalStrength, updateMembranePotentialHistory])

  // Render membrane potential graph
  const renderMembranePotentialGraph = (node: BNNNode) => {
    const history = membranePotentialHistory[node.id] || []
    if (history.length < 2) return null

    const width = 200
    const height = 100
    const padding = 20

    const minPotential = -80
    const maxPotential = 40
    const minTime = Math.min(...history.map(h => h.time))
    const maxTime = Math.max(...history.map(h => h.time))

    const scaleX = (time: number) => padding + ((time - minTime) / (maxTime - minTime)) * (width - 2 * padding)
    const scaleY = (potential: number) => height - padding - ((potential - minPotential) / (maxPotential - minPotential)) * (height - 2 * padding)

    const pathData = history.map((point, index) =>
      `${index === 0 ? 'M' : 'L'} ${scaleX(point.time)} ${scaleY(point.potential)}`
    ).join(' ')

    return (
      <div className="absolute bg-card border rounded-lg p-2 shadow-lg" style={{
        left: node.x + 30,
        top: node.y - 60,
        zIndex: 10
      }}>
        <h4 className="text-xs font-semibold mb-1">Membrane Potential</h4>
        <svg width={width} height={height} className="border rounded">
          {/* Grid lines */}
          <defs>
            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
              <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#374151" strokeWidth="0.5" opacity="0.3"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />

          {/* Threshold line */}
          <line
            x1={padding}
            y1={scaleY(spikeThreshold[0])}
            x2={width - padding}
            y2={scaleY(spikeThreshold[0])}
            stroke="#ef4444"
            strokeWidth="1"
            strokeDasharray="3,3"
          />
          <text x={padding + 5} y={scaleY(spikeThreshold[0]) - 2} fontSize="8" fill="#ef4444">{spikeThreshold[0]}mV (threshold)</text>

          {/* Resting potential line */}
          <line
            x1={padding}
            y1={scaleY(-70)}
            x2={width - padding}
            y2={scaleY(-70)}
            stroke="#6b7280"
            strokeWidth="1"
            strokeDasharray="3,3"
          />
          <text x={padding + 5} y={scaleY(-70) - 2} fontSize="8" fill="#6b7280">-70mV (rest)</text>

          {/* Membrane potential curve */}
          <path
            d={pathData}
            fill="none"
            stroke="#10b981"
            strokeWidth="2"
          />

          {/* Current potential dot */}
          {history.length > 0 && (
            <circle
              cx={scaleX(history[history.length - 1].time)}
              cy={scaleY(history[history.length - 1].potential)}
              r="3"
              fill="#10b981"
            />
          )}

          {/* Axes labels */}
          <text x={width/2} y={height - 5} textAnchor="middle" fontSize="8" fill="currentColor">Time</text>
          <text x={10} y={height/2} textAnchor="middle" fontSize="8" fill="currentColor" transform={`rotate(-90, 10, ${height/2})`}>mV</text>
        </svg>
        <p className="text-xs text-muted-foreground mt-1">
          Current: {node.membrane_potential.toFixed(1)}mV
        </p>
      </div>
    )
  }

  const getNodeColor = (node: Node) => {
    if (node.active) return "#10b981" // Active green
    if (node.type === "input") return "#059669" // Input green
    if (node.type === "output") return "#84cc16" // Output lime
    return "#475569" // Hidden gray
  }

  const getNodeSize = (node: Node) => {
    const baseSize = node.type === "input" || node.type === "output" ? 20 : 16
    return node.active ? baseSize + 4 : baseSize
  }



  // Render BNN node with detailed anatomical structure (like the reference image)
  const renderBNNNode = (node: BNNNode) => (
    <g key={node.id} className={`bnn-node ${node.firing ? 'firing' : ''}`}>
      {/* Dendrites - detailed tree-like branching structure */}
      {node.dendrites.map((dendrite, index) => (
        <g key={`dendrite-${index}`}>
          {/* Main dendrite trunk */}
          <line
            x1={node.x}
            y1={node.y}
            x2={dendrite.x}
            y2={dendrite.y}
            stroke={node.active ? "#6b7280" : "#4b5563"}
            strokeWidth="3"
            className="transition-all duration-300"
          />

          {/* Primary dendrite branches */}
          {[0.3, -0.3, 0.6, -0.6].map((angleOffset, branchIndex) => {
            const branchX = dendrite.x + Math.cos(dendrite.angle + angleOffset) * 15
            const branchY = dendrite.y + Math.sin(dendrite.angle + angleOffset) * 15
            return (
              <g key={`branch-${branchIndex}`}>
                <line
                  x1={dendrite.x}
                  y1={dendrite.y}
                  x2={branchX}
                  y2={branchY}
                  stroke={node.active ? "#6b7280" : "#4b5563"}
                  strokeWidth="2"
                  className="transition-all duration-300"
                />

                {/* Secondary branches */}
                {[0.2, -0.2].map((subAngle, subIndex) => {
                  const subBranchX = branchX + Math.cos(dendrite.angle + angleOffset + subAngle) * 8
                  const subBranchY = branchY + Math.sin(dendrite.angle + angleOffset + subAngle) * 8
                  return (
                    <g key={`sub-${subIndex}`}>
                      <line
                        x1={branchX}
                        y1={branchY}
                        x2={subBranchX}
                        y2={subBranchY}
                        stroke={node.active ? "#6b7280" : "#4b5563"}
                        strokeWidth="1.5"
                        className="transition-all duration-300"
                      />

                      {/* Tertiary fine branches */}
                      {[0.1, -0.1, 0.3, -0.3].map((fineAngle, fineIndex) => (
                        <line
                          key={`fine-${fineIndex}`}
                          x1={subBranchX}
                          y1={subBranchY}
                          x2={subBranchX + Math.cos(dendrite.angle + angleOffset + subAngle + fineAngle) * 4}
                          y2={subBranchY + Math.sin(dendrite.angle + angleOffset + subAngle + fineAngle) * 4}
                          stroke={node.active ? "#6b7280" : "#4b5563"}
                          strokeWidth="1"
                          className="transition-all duration-300"
                        />
                      ))}
                    </g>
                  )
                })}
              </g>
            )
          })}
        </g>
      ))}

      {/* Axon - single long projection */}
      <line
        x1={node.x}
        y1={node.y}
        x2={node.axon.x}
        y2={node.axon.y}
        stroke={node.firing ? "#ef4444" : node.active ? "#059669" : "#6b7280"}
        strokeWidth={node.firing ? "4" : "3"}
        className="transition-all duration-300"
      />

      {/* Synaptic terminals - detailed branching endings */}
      <g>
        {/* Main terminal branches */}
        {[0, 0.3, -0.3, 0.6, -0.6, 0.9, -0.9].map((angleOffset, termIndex) => {
          const termX = node.axon.x + Math.cos(node.axon.angle + angleOffset) * 12
          const termY = node.axon.y + Math.sin(node.axon.angle + angleOffset) * 12
          return (
            <g key={`terminal-${termIndex}`}>
              <line
                x1={node.axon.x}
                y1={node.axon.y}
                x2={termX}
                y2={termY}
                stroke={node.firing ? "#ef4444" : node.active ? "#059669" : "#6b7280"}
                strokeWidth="2"
                className="transition-all duration-300"
              />

              {/* Synaptic boutons (terminal swellings) */}
              <circle
                cx={termX}
                cy={termY}
                r={node.firing ? "3" : "2"}
                fill={node.firing ? "#ef4444" : node.active ? "#059669" : "#6b7280"}
                className="transition-all duration-300"
              />

              {/* Fine terminal branches */}
              {[0.2, -0.2].map((fineAngle, fineIndex) => (
                <line
                  key={`fine-term-${fineIndex}`}
                  x1={termX}
                  y1={termY}
                  x2={termX + Math.cos(node.axon.angle + angleOffset + fineAngle) * 6}
                  y2={termY + Math.sin(node.axon.angle + angleOffset + fineAngle) * 6}
                  stroke={node.firing ? "#ef4444" : node.active ? "#059669" : "#6b7280"}
                  strokeWidth="1"
                  className="transition-all duration-300"
                />
              ))}
            </g>
          )
        })}
      </g>

      {/* Cell body (soma) - detailed anatomical structure */}
      <circle
        cx={node.x}
        cy={node.y}
        r={node.firing ? "18" : "16"}
        fill={node.firing ? "#fbbf24" : node.active ? "#f59e0b" : "#d97706"}
        stroke={node.firing ? "#ef4444" : selectedBNNNode?.id === node.id ? "#10b981" : "#92400e"}
        strokeWidth={selectedBNNNode?.id === node.id ? "3" : "2"}
        className={`transition-all duration-300 cursor-pointer ${node.firing ? 'animate-pulse' : ''}`}
        onClick={() => {
          setSelectedBNNNode(selectedBNNNode?.id === node.id ? null : node)
          updateMembranePotentialHistory(node.id, node.membrane_potential, currentTime)
        }}
      />

      {/* Cell membrane detail */}
      <circle
        cx={node.x}
        cy={node.y}
        r={node.firing ? "17" : "15"}
        fill="none"
        stroke="#92400e"
        strokeWidth="1"
        strokeOpacity="0.5"
        className="transition-all duration-300"
      />

      {/* Nucleus - prominent center with nucleolus */}
      <circle
        cx={node.x}
        cy={node.y}
        r="8"
        fill="#7c2d12"
        stroke="#92400e"
        strokeWidth="1"
        className="transition-all duration-300"
      />

      {/* Nucleolus - small dark spot in nucleus */}
      <circle
        cx={node.x + 2}
        cy={node.y - 1}
        r="2"
        fill="#451a03"
        className="transition-all duration-300"
      />

      {/* Organelles - small dots representing mitochondria, etc. */}
      {[
        { x: node.x - 8, y: node.y + 4, r: 1.5 },
        { x: node.x + 6, y: node.y - 6, r: 1 },
        { x: node.x - 4, y: node.y - 8, r: 1.2 },
        { x: node.x + 8, y: node.y + 6, r: 1 }
      ].map((organelle, index) => (
        <circle
          key={`organelle-${index}`}
          cx={organelle.x}
          cy={organelle.y}
          r={organelle.r}
          fill="#a16207"
          opacity="0.7"
          className="transition-all duration-300"
        />
      ))}

      {/* Membrane potential text */}
      <text
        x={node.x}
        y={node.y + 2}
        textAnchor="middle"
        fontSize="9"
        fill="white"
        className="pointer-events-none font-mono font-bold"
      >
        {node.membrane_potential.toFixed(0)}
      </text>

      {/* Educational Labels */}
      <text
        x={node.x}
        y={node.y - 30}
        textAnchor="middle"
        fontSize="11"
        fill="currentColor"
        className="font-sans font-semibold text-foreground pointer-events-none"
      >
        {node.type === "input" ? "Input Neuron" : node.type === "output" ? "Output Neuron" : "Hidden Neuron"}
      </text>

      {/* Anatomical labels - toggleable */}
      {showAnatomicalLabels && (
        <>
          {/* Cell Body label with pointer */}
          <g>
            <line
              x1={node.x - 20}
              y1={node.y + 25}
              x2={node.x - 8}
              y2={node.y + 8}
              stroke="#6b7280"
              strokeWidth="1"
              markerEnd="url(#arrowhead)"
            />
            <text
              x={node.x - 35}
              y={node.y + 28}
              textAnchor="middle"
              fontSize="10"
              fill="#6b7280"
              className="font-sans font-medium pointer-events-none"
            >
              Cell Body
            </text>
          </g>

          {/* Nucleus label */}
          <g>
            <line
              x1={node.x + 15}
              y1={node.y - 15}
              x2={node.x + 4}
              y2={node.y - 4}
              stroke="#6b7280"
              strokeWidth="1"
              markerEnd="url(#arrowhead)"
            />
            <text
              x={node.x + 25}
              y={node.y - 12}
              textAnchor="start"
              fontSize="9"
              fill="#6b7280"
              className="font-sans font-medium pointer-events-none"
            >
              Nucleus
            </text>
          </g>

          {/* Dendrites label */}
          {node.dendrites.length > 0 && (
            <g>
              <line
                x1={node.dendrites[0].x - 10}
                y1={node.dendrites[0].y - 15}
                x2={node.dendrites[0].x}
                y2={node.dendrites[0].y - 5}
                stroke="#6b7280"
                strokeWidth="1"
                markerEnd="url(#arrowhead)"
              />
              <text
                x={node.dendrites[0].x - 15}
                y={node.dendrites[0].y - 18}
                textAnchor="middle"
                fontSize="9"
                fill="#6b7280"
                className="font-sans font-medium pointer-events-none"
              >
                Dendrites
              </text>
            </g>
          )}

          {/* Axon label */}
          <g>
            <line
              x1={node.axon.x + 10}
              y1={node.axon.y + 20}
              x2={node.axon.x}
              y2={node.axon.y + 5}
              stroke="#6b7280"
              strokeWidth="1"
              markerEnd="url(#arrowhead)"
            />
            <text
              x={node.axon.x + 15}
              y={node.axon.y + 25}
              textAnchor="start"
              fontSize="9"
              fill="#6b7280"
              className="font-sans font-medium pointer-events-none"
            >
              Axon
            </text>
          </g>

          {/* Synaptic terminals label */}
          <g>
            <line
              x1={node.axon.x + 20}
              y1={node.axon.y - 10}
              x2={node.axon.x + 8}
              y2={node.axon.y - 2}
              stroke="#6b7280"
              strokeWidth="1"
              markerEnd="url(#arrowhead)"
            />
            <text
              x={node.axon.x + 25}
              y={node.axon.y - 5}
              textAnchor="start"
              fontSize="8"
              fill="#6b7280"
              className="font-sans font-medium pointer-events-none"
            >
              Synaptic
            </text>
            <text
              x={node.axon.x + 25}
              y={node.axon.y + 5}
              textAnchor="start"
              fontSize="8"
              fill="#6b7280"
              className="font-sans font-medium pointer-events-none"
            >
              terminals
            </text>
          </g>
        </>
      )}
    </g>
  )

  if (!mounted) {
    return (
      <div className="min-h-screen bg-background text-foreground flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-primary/20 animate-pulse"></div>
          <p className="text-muted-foreground">Loading Neural Network Visualizer...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background" suppressHydrationWarning>
      <header className="border-b border-border bg-card">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Brain className="h-8 w-8 text-primary" />
              <div>
                <h1 className="text-3xl font-bold text-foreground">Neural Network Visualizer</h1>
                <p className="text-muted-foreground">Interactive deep learning demonstration</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant={showComparison ? "default" : "outline"}
                onClick={() => setShowComparison(!showComparison)}
                className="flex items-center gap-2"
              >
                <Brain className="h-4 w-4" />
                {showComparison ? "Hide Brain" : "Compare with Brain"}
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Warning Banner */}
        <Card className="mb-6 border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-amber-800 dark:text-amber-200">Educational Simulation</h3>
                <p className="text-sm text-amber-700 dark:text-amber-300 mt-1">
                  This is a simplified visualization for educational purposes. Real neural networks involve complex mathematical operations,
                  backpropagation, gradient descent, and sophisticated optimization techniques not shown here.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Brain vs ANN Comparison */}
        {showComparison && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-6 w-6 text-primary" />
                Biological Neural Network (Brain) vs Artificial Neural Network
              </CardTitle>
              <CardDescription>
                Compare how signals propagate through the human brain versus an artificial neural network
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid lg:grid-cols-2 gap-8">
                {/* Brain Visualization */}
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold">Human Brain</h3>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant={selectedStimulus === "visual" ? "default" : "outline"}
                        onClick={() => setSelectedStimulus("visual")}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        Visual
                      </Button>
                      <Button
                        size="sm"
                        variant={selectedStimulus === "auditory" ? "default" : "outline"}
                        onClick={() => setSelectedStimulus("auditory")}
                      >
                        <Ear className="h-4 w-4 mr-1" />
                        Audio
                      </Button>
                      <Button
                        size="sm"
                        variant={selectedStimulus === "motor" ? "default" : "outline"}
                        onClick={() => setSelectedStimulus("motor")}
                      >
                        <Hand className="h-4 w-4 mr-1" />
                        Motor
                      </Button>
                      <Button
                        size="sm"
                        variant={selectedStimulus === "cognitive" ? "default" : "outline"}
                        onClick={() => setSelectedStimulus("cognitive")}
                      >
                        <Target className="h-4 w-4 mr-1" />
                        Cognitive
                      </Button>
                    </div>
                  </div>



                  <div className="relative">
                    <svg width="100%" height="320" viewBox="0 0 600 320" className="border rounded-lg bg-card">
                      {/* Brain outline */}
                      <path
                        d="M 150 150 Q 150 80 250 80 Q 350 80 450 100 Q 500 120 500 180 Q 500 240 450 260 Q 350 280 250 280 Q 150 280 150 220 Q 120 200 120 180 Q 120 160 150 150"
                        fill="none"
                        stroke="#374151"
                        strokeWidth="2"
                        className="opacity-30"
                      />

                      {/* Brain regions */}
                      {brainRegions.map((region) => (
                        <g key={region.id}>
                          <rect
                            x={region.x}
                            y={region.y}
                            width={region.width}
                            height={region.height}
                            fill={region.active ? region.color : "#374151"}
                            stroke="#ffffff"
                            strokeWidth="1"
                            rx="4"
                            className={`transition-all duration-300 ${region.active ? 'opacity-100' : 'opacity-60'}`}
                          />
                          <text
                            x={region.x + region.width / 2}
                            y={region.y + region.height / 2 + 4}
                            textAnchor="middle"
                            fontSize="10"
                            fill="white"
                            className="font-semibold pointer-events-none"
                          >
                            {region.name}
                          </text>
                        </g>
                      ))}

                      {/* Brain signals */}
                      {brainSignals.map((signal) => (
                        <circle
                          key={signal.id}
                          cx={signal.x}
                          cy={signal.y}
                          r="4"
                          fill="#10b981"
                          className="animate-pulse"
                        >
                          <animateMotion
                            dur="0.8s"
                            path={`M ${signal.x} ${signal.y} L ${signal.targetX} ${signal.targetY}`}
                            fill="freeze"
                          />
                        </circle>
                      ))}
                    </svg>

                    <Button
                      onClick={() => simulateBrainSignal(selectedStimulus)}
                      className="mt-4 w-full"
                      disabled={brainSignals.length > 0}
                    >
                      <Zap className="h-4 w-4 mr-2" />
                      Send {selectedStimulus.charAt(0).toUpperCase() + selectedStimulus.slice(1)} Signal
                    </Button>

                    {/* Signal pathway info */}
                    <div className="mt-4 p-3 bg-muted rounded-lg">
                      <h4 className="font-semibold text-sm mb-2">
                        {selectedStimulus.charAt(0).toUpperCase() + selectedStimulus.slice(1)} Processing Pathway
                      </h4>
                      <p className="text-xs text-muted-foreground">
                        {selectedStimulus === "visual" && "Visual signals: Eyes → Occipital Cortex → Parietal Cortex → Frontal Cortex"}
                        {selectedStimulus === "auditory" && "Audio signals: Ears → Temporal Cortex → Thalamus → Frontal Cortex"}
                        {selectedStimulus === "motor" && "Motor commands: Frontal Cortex → Motor Cortex → Brainstem → Muscles"}
                        {selectedStimulus === "cognitive" && "Cognitive processing: Frontal Cortex → Hippocampus → Thalamus → Parietal Cortex"}
                      </p>
                    </div>

                    {/* Brain regions legend */}
                    <div className="mt-4 grid grid-cols-2 gap-2 text-xs">
                      {brainRegions.slice(0, 6).map((region) => (
                        <div key={region.id} className="flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded"
                            style={{ backgroundColor: region.color }}
                          />
                          <span className="truncate">{region.name}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* ANN Comparison */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Artificial Neural Network</h3>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="p-3 bg-muted rounded-lg">
                        <h4 className="font-semibold text-primary mb-2">Biological Neurons (BNN)</h4>
                        <ul className="space-y-1 text-xs">
                          <li>• Cell body (soma) with nucleus</li>
                          <li>• Dendrites receive signals</li>
                          <li>• Axon transmits signals</li>
                          <li>• Synapses for connections</li>
                          <li>• Spike-based communication</li>
                          <li>• ~86 billion neurons</li>
                          <li>• Self-repair and plasticity</li>
                        </ul>
                      </div>
                      <div className="p-3 bg-muted rounded-lg">
                        <h4 className="font-semibold text-secondary mb-2">Artificial Neurons (ANN)</h4>
                        <ul className="space-y-1 text-xs">
                          <li>• Mathematical nodes</li>
                          <li>• Weighted input summation</li>
                          <li>• Activation functions</li>
                          <li>• Matrix operations</li>
                          <li>• Continuous values</li>
                          <li>• Thousands to millions</li>
                          <li>• Fixed architecture</li>
                        </ul>
                      </div>
                    </div>

                    <div className="p-4 bg-amber-50 dark:bg-amber-950 rounded-lg border border-amber-200 dark:border-amber-800">
                      <h4 className="font-semibold text-amber-800 dark:text-amber-200 mb-2">Key Differences</h4>
                      <div className="text-xs text-amber-700 dark:text-amber-300 space-y-1">
                        <p><strong>Signal Type:</strong> Biological uses electrical spikes, Artificial uses continuous values</p>
                        <p><strong>Components:</strong> Bio has dendrites/axons/synapses, AI has mathematical weights</p>
                        <p><strong>Learning:</strong> Bio learns through synaptic plasticity, AI through backpropagation</p>
                        <p><strong>Speed:</strong> Bio ~100Hz firing rate, AI can process millions of operations/sec</p>
                        <p><strong>Fault Tolerance:</strong> Bio self-repairs and adapts, AI fails with damaged nodes</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="grid lg:grid-cols-4 gap-6">
          {/* Control Panel */}
          <div className="lg:col-span-1 space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Settings className="h-5 w-5" />
                  Controls
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Button
                    onClick={() => {
                      if (simulationMode === "ANN") {
                        setIsRunning(!isRunning)
                      } else {
                        runBNNPropagation()
                      }
                    }}
                    disabled={simulationMode === "ANN" ? currentEpoch >= epochs[0] : bnnRunning}
                    className="flex-1"
                  >
                    {simulationMode === "ANN" ? (
                      <>
                        {isRunning ? <Pause className="h-4 w-4 mr-2" /> : <Play className="h-4 w-4 mr-2" />}
                        {isRunning ? "Pause" : "Train"}
                      </>
                    ) : (
                      <>
                        <Zap className="h-4 w-4 mr-2" />
                        {bnnRunning ? "Spiking..." : "Send Spikes"}
                      </>
                    )}
                  </Button>
                  <Button variant="outline" onClick={resetNetwork}>
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </div>

                <div className="space-y-3">
                  {simulationMode === "ANN" ? (
                    <>
                      <div>
                        <label className="text-sm font-medium mb-2 block">Learning Rate</label>
                        <Slider
                          value={learningRate}
                          onValueChange={setLearningRate}
                          max={1}
                          min={0.001}
                          step={0.001}
                          className="w-full"
                        />
                        <span className="text-xs text-muted-foreground">{learningRate[0].toFixed(3)}</span>
                      </div>

                      <div>
                        <label className="text-sm font-medium mb-2 block">Max Epochs</label>
                        <Slider
                          value={epochs}
                          onValueChange={(value) => {
                            setEpochs(value)
                            if (currentEpoch >= value[0]) {
                              setCurrentEpoch(0)
                              setIsRunning(false)
                            }
                          }}
                          max={500}
                          min={10}
                          step={10}
                          className="w-full"
                          disabled={isRunning}
                        />
                        <span className="text-xs text-muted-foreground">{epochs[0]}</span>
                      </div>
                    </>
                  ) : (
                    <>
                      <div>
                        <label className="text-sm font-medium mb-2 block">Signal Strength (EPSP/IPSP)</label>
                        <Slider
                          value={signalStrength}
                          onValueChange={setSignalStrength}
                          max={30}
                          min={5}
                          step={1}
                          className="w-full"
                        />
                        <span className="text-xs text-muted-foreground">{signalStrength[0]} mV</span>
                      </div>

                      <div>
                        <label className="text-sm font-medium mb-2 block">Spike Threshold</label>
                        <Slider
                          value={spikeThreshold}
                          onValueChange={setSpikeThreshold}
                          max={-40}
                          min={-70}
                          step={1}
                          className="w-full"
                        />
                        <span className="text-xs text-muted-foreground">{spikeThreshold[0]} mV</span>
                      </div>

                      <div className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          id="anatomicalLabels"
                          checked={showAnatomicalLabels}
                          onChange={(e) => setShowAnatomicalLabels(e.target.checked)}
                          className="rounded"
                        />
                        <label htmlFor="anatomicalLabels" className="text-sm">Show anatomical labels</label>
                      </div>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Layers className="h-5 w-5" />
                  Architecture
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Hidden Layers</label>
                    <Slider
                      value={numHiddenLayers}
                      onValueChange={setNumHiddenLayers}
                      max={5}
                      min={1}
                      step={1}
                      className="w-full"
                      disabled={isRunning}
                    />
                    <span className="text-xs text-muted-foreground">{numHiddenLayers[0]} layers</span>
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">Nodes per Hidden Layer</label>
                    <Slider
                      value={nodesPerLayer}
                      onValueChange={setNodesPerLayer}
                      max={8}
                      min={2}
                      step={1}
                      className="w-full"
                      disabled={isRunning}
                    />
                    <span className="text-xs text-muted-foreground">{nodesPerLayer[0]} nodes</span>
                  </div>

                  {simulationMode === "ANN" && (
                    <div>
                      <label className="text-sm font-medium mb-2 block">Activation Function</label>
                      <div className="grid grid-cols-2 gap-2">
                        {(Object.keys(activationFunctions) as ActivationFunction[]).map((func) => (
                          <Button
                            key={func}
                            variant={activationFunction === func ? "default" : "outline"}
                            size="sm"
                            onClick={() => setActivationFunction(func)}
                            disabled={isRunning}
                            className="text-xs"
                          >
                            {func.toUpperCase()}
                          </Button>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id="showDetails"
                      checked={showNodeDetails}
                      onChange={(e) => setShowNodeDetails(e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="showDetails" className="text-sm">Show node details</label>
                  </div>

                  {simulationMode === "ANN" && (
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        id="hybridMode"
                        checked={showHybridMode}
                        onChange={(e) => setShowHybridMode(e.target.checked)}
                        className="rounded"
                      />
                      <label htmlFor="hybridMode" className="text-sm">Hybrid mode (rate coding)</label>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Activity className="h-5 w-5" />
                  Metrics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Epoch</span>
                    <Badge variant="secondary">
                      {currentEpoch}/{epochs[0]}
                    </Badge>
                  </div>

                  <div className="space-y-1">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Loss</span>
                      <span className="text-sm font-mono">{loss.toFixed(4)}</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div
                        className="bg-destructive h-2 rounded-full transition-all duration-300"
                        style={{ width: `${Math.max(5, loss * 100)}%` }}
                      />
                    </div>
                  </div>

                  <div className="space-y-1">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Accuracy</span>
                      <span className="text-sm font-mono">{(accuracy * 100).toFixed(1)}%</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div
                        className="bg-primary h-2 rounded-full transition-all duration-300"
                        style={{ width: `${accuracy * 100}%` }}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {selectedNode && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Info className="h-5 w-5" />
                    Node Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="text-sm">
                    <strong>ID:</strong> {selectedNode.id}
                  </div>
                  <div className="text-sm">
                    <strong>Type:</strong> {selectedNode.type}
                  </div>
                  <div className="text-sm">
                    <strong>Value:</strong> {selectedNode.value.toFixed(4)}
                  </div>
                  <div className="text-sm">
                    <strong>Bias:</strong> {selectedNode.bias.toFixed(4)}
                  </div>
                  <div className="text-sm">
                    <strong>Layer:</strong> {selectedNode.layer}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Network Visualization */}
          <div className="lg:col-span-3">
            <Card className="h-[600px]">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Zap className="h-5 w-5" />
                      {simulationMode === "ANN" ? "Artificial Neural Network" : "Spiking Neural Network"}
                    </CardTitle>
                    <CardDescription>
                      {simulationMode === "ANN"
                        ? `${3}-${Array(numHiddenLayers[0]).fill(nodesPerLayer[0]).join('-')}-${2} Neural Network • Activation: ${activationFunction.toUpperCase()}`
                        : "Spike-based processing with temporal dynamics"
                      }
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant={simulationMode === "ANN" ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSimulationMode("ANN")}
                    >
                      ANN
                    </Button>
                    <Button
                      variant={simulationMode === "SNN" ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSimulationMode("SNN")}
                    >
                      SNN
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="h-full p-0">
                <svg width="100%" height="500" className="overflow-visible">
                  {/* SVG Definitions for arrows and patterns */}
                  <defs>
                    <marker
                      id="arrowhead"
                      markerWidth="10"
                      markerHeight="7"
                      refX="9"
                      refY="3.5"
                      orient="auto"
                    >
                      <polygon
                        points="0 0, 10 3.5, 0 7"
                        fill="#6b7280"
                      />
                    </marker>
                  </defs>
                  {simulationMode === "ANN" ? (
                    <>
                      {/* ANN Mode - Render connections */}
                      {connections.map((conn, index) => {
                        const fromNode = nodes.find((n) => n.id === conn.from)
                        const toNode = nodes.find((n) => n.id === conn.to)
                        if (!fromNode || !toNode) return null

                        return (
                          <line
                            key={index}
                            x1={fromNode.x}
                            y1={fromNode.y}
                            x2={toNode.x}
                            y2={toNode.y}
                            stroke={conn.active ? "#10b981" : "#475569"}
                            strokeWidth={conn.active ? 2 : 1}
                            strokeOpacity={conn.active ? 1 : 0.3}
                            className={`neural-connection ${conn.active ? "active" : ""}`}
                          />
                        )
                      })}

                      {/* ANN Nodes */}
                      {nodes.map((node) => (
                        <g key={node.id}>
                          <circle
                            cx={node.x}
                            cy={node.y}
                            r={getNodeSize(node)}
                            fill={getNodeColor(node)}
                            stroke="#ffffff"
                            strokeWidth={2}
                            className={`neural-node cursor-pointer ${node.active ? "active" : ""}`}
                            onClick={() => setSelectedNode(node)}
                          />
                          <text
                            x={node.x}
                            y={node.y + 4}
                            textAnchor="middle"
                            fontSize="12"
                            fill="white"
                            className="pointer-events-none font-mono font-semibold"
                          >
                            {(showHybridMode === true && simulationMode === "ANN") ? `${(node.value * 100).toFixed(0)}Hz` : node.value.toFixed(2)}
                          </text>

                          {/* Node details above node */}
                          {showNodeDetails && (
                            <g className="pointer-events-none">
                              <text
                                x={node.x}
                                y={node.y - getNodeSize(node) - 12}
                                textAnchor="middle"
                                fontSize="12"
                                fill="currentColor"
                                className="font-mono text-muted-foreground"
                              >
                                {node.id}
                              </text>
                              <text
                                x={node.x}
                                y={node.y - getNodeSize(node) - 26}
                                textAnchor="middle"
                                fontSize="10"
                                fill="currentColor"
                                className="font-mono text-muted-foreground"
                              >
                                {(showHybridMode === true && simulationMode === "ANN") ? "spike rate" : `bias: ${node.bias.toFixed(2)}`}
                              </text>
                            </g>
                          )}
                        </g>
                      ))}
                    </>
                  ) : (
                    <>
                      {/* SNN Mode - Render BNN-style connections */}
                      {bnnNodes.map((fromNode) =>
                        bnnNodes
                          .filter(toNode => toNode.layer === fromNode.layer + 1)
                          .map((toNode) => (
                            <line
                              key={`snn-${fromNode.id}-${toNode.id}`}
                              x1={fromNode.axon.x}
                              y1={fromNode.axon.y}
                              x2={toNode.x}
                              y2={toNode.y}
                              stroke="#374151"
                              strokeWidth="1"
                              strokeOpacity="0.3"
                              strokeDasharray="2,2"
                            />
                          ))
                      )}

                      {/* SNN Nodes */}
                      {bnnNodes.map(renderBNNNode)}

                      {/* Render spikes in SNN mode */}
                      {spikes.map((spike) => (
                        <circle
                          key={spike.id}
                          cx={spike.x}
                          cy={spike.y}
                          r="3"
                          fill="#ef4444"
                          className="animate-pulse"
                        >
                          <animateMotion
                            dur="0.8s"
                            path={`M ${spike.x} ${spike.y} L ${spike.targetX} ${spike.targetY}`}
                            fill="freeze"
                          />
                        </circle>
                      ))}
                    </>
                  )}

                  {/* Dynamic layer labels */}
                  {(() => {
                    const totalLayers = numHiddenLayers[0] + 2
                    const layerSpacing = 800 / (totalLayers + 1)
                    const labels = []

                    // Input layer
                    labels.push(
                      <text key="input" x={layerSpacing} y={50} textAnchor="middle" className="text-sm font-medium fill-foreground">
                        {simulationMode === "ANN" ? "Input Layer" : "SNN Input Layer"}
                      </text>
                    )

                    // Hidden layers
                    for (let i = 1; i <= numHiddenLayers[0]; i++) {
                      labels.push(
                        <text key={`hidden-${i}`} x={layerSpacing * (i + 1)} y={50} textAnchor="middle" className="text-sm font-medium fill-foreground">
                          {simulationMode === "ANN" ? `Hidden Layer ${i}` : `SNN Hidden Layer ${i}`}
                        </text>
                      )
                    }

                    // Output layer
                    labels.push(
                      <text key="output" x={layerSpacing * totalLayers} y={50} textAnchor="middle" className="text-sm font-medium fill-foreground">
                        {simulationMode === "ANN" ? "Output Layer" : "SNN Output Layer"}
                      </text>
                    )

                    return labels
                  })()}
                </svg>

                {/* Membrane potential graph overlay */}
                {simulationMode === "SNN" && selectedBNNNode && renderMembranePotentialGraph(selectedBNNNode)}
              </CardContent>
            </Card>

            <div className="mt-6 grid md:grid-cols-4 gap-4">
              {simulationMode === "ANN" ? (
                <>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="w-4 h-4 rounded-full bg-primary"></div>
                        <div>
                          <p className="font-medium text-sm">Input Nodes</p>
                          <p className="text-xs text-muted-foreground">
                            {showHybridMode ? "Spike rates (Hz)" : "Raw data features"}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="w-4 h-4 rounded-full bg-muted"></div>
                        <div>
                          <p className="font-medium text-sm">Hidden Nodes</p>
                          <p className="text-xs text-muted-foreground">
                            {showHybridMode ? "Rate coding" : "Feature extraction"}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="w-4 h-4 rounded-full bg-secondary"></div>
                        <div>
                          <p className="font-medium text-sm">Output Nodes</p>
                          <p className="text-xs text-muted-foreground">
                            {showHybridMode ? "Output rates" : "Final predictions"}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <Zap className="w-4 h-4 text-primary" />
                        <div>
                          <p className="font-medium text-sm">
                            {showHybridMode ? "Rate Coding" : `Activation: ${activationFunction.toUpperCase()}`}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {showHybridMode ? "Frequency ≈ activation" : (
                              <>
                                {activationFunction === 'sigmoid' && 'Output: 0 to 1'}
                                {activationFunction === 'relu' && 'Output: 0 to ∞'}
                                {activationFunction === 'tanh' && 'Output: -1 to 1'}
                                {activationFunction === 'leaky_relu' && 'Output: -∞ to ∞'}
                              </>
                            )}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </>
              ) : (
                <>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="w-4 h-4 rounded-full bg-amber-600"></div>
                        <div>
                          <p className="font-medium text-sm">Spiking Neurons</p>
                          <p className="text-xs text-muted-foreground">Click to see membrane potential</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="w-4 h-4 rounded-full bg-red-500 animate-pulse"></div>
                        <div>
                          <p className="font-medium text-sm">Action Potentials</p>
                          <p className="text-xs text-muted-foreground">Electrical spikes</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="w-4 h-1 bg-emerald-600"></div>
                        <div>
                          <p className="font-medium text-sm">Synapses</p>
                          <p className="text-xs text-muted-foreground">Spike transmission</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <Activity className="w-4 h-4 text-primary" />
                        <div>
                          <p className="font-medium text-sm">Temporal Dynamics</p>
                          <p className="text-xs text-muted-foreground">Time-based processing</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}
            </div>

            {/* Hybrid Mode Explanation */}
            {showHybridMode && simulationMode === "ANN" && (
              <Card className="mt-4">
                <CardContent className="p-4">
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <Activity className="h-4 w-4" />
                    Hybrid Mode: Rate Coding Bridge
                  </h4>
                  <div className="text-sm space-y-2">
                    <p>
                      <strong>Rate Coding:</strong> In biological neurons, information can be encoded in the frequency of spikes.
                      Higher activation values correspond to higher spike rates.
                    </p>
                    <div className="grid md:grid-cols-2 gap-4 mt-3">
                      <div className="p-3 bg-muted rounded-lg">
                        <h5 className="font-medium text-primary mb-1">ANN Activation → Spike Rate</h5>
                        <ul className="text-xs space-y-1">
                          <li>• 0.0 activation = 0 Hz (no spikes)</li>
                          <li>• 0.5 activation = 50 Hz (moderate)</li>
                          <li>• 1.0 activation = 100 Hz (high rate)</li>
                        </ul>
                      </div>
                      <div className="p-3 bg-muted rounded-lg">
                        <h5 className="font-medium text-secondary mb-1">Biological Relevance</h5>
                        <ul className="text-xs space-y-1">
                          <li>• Neurons fire 1-100+ Hz typically</li>
                          <li>• Rate coding is one neural code</li>
                          <li>• Bridges ANN and SNN concepts</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>


        </div>
      </div>
    </div>
  )
}

export default NeuralNetwork
