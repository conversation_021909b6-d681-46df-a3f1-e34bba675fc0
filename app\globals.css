@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Clean dark mode for neural network theme */
  --background: #0f172a; /* Very dark slate background */
  --foreground: #f1f5f9; /* Light text */
  --card: #1e293b; /* Dark card background */
  --card-foreground: #f1f5f9; /* Light text on cards */
  --popover: #1e293b; /* Dark popover */
  --popover-foreground: #f1f5f9; /* Light popover text */
  --primary: #10b981; /* Emerald green for primary elements */
  --primary-foreground: #ffffff; /* White text on primary */
  --secondary: #374151; /* Gray for secondary elements */
  --secondary-foreground: #f1f5f9; /* Light text on secondary */
  --muted: #374151; /* Muted gray background */
  --muted-foreground: #9ca3af; /* Muted text */
  --accent: #059669; /* Darker emerald for accents */
  --accent-foreground: #ffffff; /* White text on accent */
  --destructive: #ef4444; /* Red for errors */
  --destructive-foreground: #ffffff; /* White text on destructive */
  --border: #374151; /* <PERSON> border */
  --input: #1e293b; /* Dark input background */
  --ring: rgba(16, 185, 129, 0.3); /* Emerald ring for focus */
  --chart-1: #10b981; /* Chart colors */
  --chart-2: #059669;
  --chart-3: #84cc16;
  --chart-4: #6366f1;
  --chart-5: #ef4444;
  --radius: 0.5rem;
  --sidebar: #1e293b; /* Dark sidebar */
  --sidebar-foreground: #f1f5f9; /* Light sidebar text */
  --sidebar-primary: #10b981; /* Primary sidebar color */
  --sidebar-primary-foreground: #ffffff; /* Primary sidebar text */
  --sidebar-accent: #059669; /* Accent sidebar color */
  --sidebar-accent-foreground: #ffffff; /* Accent sidebar text */
  --sidebar-border: #374151; /* Sidebar border */
  --sidebar-ring: rgba(16, 185, 129, 0.3); /* Sidebar ring */
}



@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Added neural network specific animations and styles */
@keyframes pulse-node {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(5, 150, 105, 0.7);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba(5, 150, 105, 0);
  }
}

@keyframes flow-connection {
  0% {
    stroke-dashoffset: 20;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

.neural-node {
  transition: all 0.3s ease;
}

.neural-node.active {
  animation: pulse-node 1s ease-in-out;
}

.neural-connection {
  stroke-dasharray: 5, 5;
  transition: all 0.3s ease;
}

.neural-connection.active {
  animation: flow-connection 1s linear infinite;
  stroke-width: 3;
}

/* Biological neuron animations */
@keyframes neuron-fire {
  0% {
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.2);
    filter: brightness(1.5);
  }
  100% {
    transform: scale(1);
    filter: brightness(1);
  }
}

.biological-neuron.firing {
  animation: neuron-fire 0.3s ease-in-out;
}

.biological-neuron.firing circle {
  filter: drop-shadow(0 0 4px rgba(239, 68, 68, 0.6));
}

/* BNN spike animations */
@keyframes spike-pulse {
  0% {
    r: 2;
    opacity: 1;
  }
  50% {
    r: 6;
    opacity: 0.8;
  }
  100% {
    r: 2;
    opacity: 1;
  }
}

.bnn-node.firing {
  animation: neuron-fire 0.3s ease-in-out;
}

.bnn-node.firing circle {
  filter: drop-shadow(0 0 6px rgba(239, 68, 68, 0.8));
}

/* Spike trail effect */
@keyframes spike-trail {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.5);
  }
}
